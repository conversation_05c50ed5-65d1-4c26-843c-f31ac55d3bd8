{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ES2020", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowImportingTsExtensions": true, "types": ["vite/client"]}, "include": ["electron/**/*", "src/**/*"], "references": [{"path": "./tsconfig.node.json"}]}