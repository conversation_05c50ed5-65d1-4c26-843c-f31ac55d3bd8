// Solutions.tsx
import React, { useState, useEffect, useRef } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism"

import ScreenshotQueue from "../components/Queue/ScreenshotQueue"

import { ProblemStatementData, MCQData, ExtendedProblemStatementData } from "../types/solutions"
import SolutionCommands from "../components/Solutions/SolutionCommands"
import MCQSection from "../components/Solutions/MCQSection"
import Debug from "./Debug"
import { useToast } from "../contexts/toast"
import { COMMAND_KEY } from "../utils/platform"

export const ContentSection = ({
  title,
  content,
  isLoading
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide">
      {title}
    </h2>
    {isLoading ? (
      <div className="mt-4 flex">
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Extracting problem statement...
        </p>
      </div>
    ) : (
      <div className="text-[13px] leading-[1.4] text-gray-100 max-w-[600px]">
        {content}
      </div>
    )}
  </div>
)
const SolutionSection = ({
  title,
  content,
  isLoading,
  currentLanguage
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
  currentLanguage: string
}) => {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = () => {
    if (typeof content === "string") {
      navigator.clipboard.writeText(content).then(() => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      })
    }
  }

  return (
    <div className="space-y-2 relative">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        {title}
      </h2>
      {isLoading ? (
        <div className="space-y-1.5">
          <div className="mt-4 flex">
            <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
              Loading solutions...
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full relative">
          <button
            onClick={copyToClipboard}
            className="absolute top-2 right-2 text-xs text-white bg-white/10 hover:bg-white/20 rounded px-2 py-1 transition"
          >
            {copied ? "Copied!" : "Copy"}
          </button>
          <SyntaxHighlighter
            showLineNumbers
            language={currentLanguage == "golang" ? "go" : currentLanguage}
            style={dracula}
            customStyle={{
              maxWidth: "100%",
              margin: 0,
              padding: "1rem",
              whiteSpace: "pre-wrap",
              wordBreak: "break-all",
              backgroundColor: "rgba(22, 27, 34, 0.5)"
            }}
            wrapLongLines={true}
          >
            {content as string}
          </SyntaxHighlighter>
        </div>
      )}
    </div>
  )
}

export const ComplexitySection = ({
  timeComplexity,
  spaceComplexity,
  isLoading
}: {
  timeComplexity: string | null
  spaceComplexity: string | null
  isLoading: boolean
}) => {
  // Helper to ensure we have proper complexity values
  const formatComplexity = (complexity: string | null): string => {
    // Default if no complexity returned by LLM
    if (!complexity || complexity.trim() === "") {
      return "Complexity not available";
    }

    const bigORegex = /O\([^)]+\)/i;
    // Return the complexity as is if it already has Big O notation
    if (bigORegex.test(complexity)) {
      return complexity;
    }

    // Concat Big O notation to the complexity
    return `O(${complexity})`;
  };

  const formattedTimeComplexity = formatComplexity(timeComplexity);
  const formattedSpaceComplexity = formatComplexity(spaceComplexity);

  return (
    <div className="space-y-2">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        Complexity
      </h2>
      {isLoading ? (
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Calculating complexity...
        </p>
      ) : (
        <div className="space-y-3">
          <div className="text-[13px] leading-[1.4] text-gray-100 bg-white/5 rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
              <div>
                <strong>Time:</strong> {formattedTimeComplexity}
              </div>
            </div>
          </div>
          <div className="text-[13px] leading-[1.4] text-gray-100 bg-white/5 rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
              <div>
                <strong>Space:</strong> {formattedSpaceComplexity}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export interface SolutionsProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}
const Solutions: React.FC<SolutionsProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const queryClient = useQueryClient()
  const contentRef = useRef<HTMLDivElement>(null)

  const [debugProcessing, setDebugProcessing] = useState(false)
  const [problemStatementData, setProblemStatementData] =
    useState<ExtendedProblemStatementData | null>(null)
  const [solutionData, setSolutionData] = useState<string | null>(null)
  const [thoughtsData, setThoughtsData] = useState<string[] | null>(null)
  const [timeComplexityData, setTimeComplexityData] = useState<string | null>(
    null
  )
  const [spaceComplexityData, setSpaceComplexityData] = useState<string | null>(
    null
  )
  const [mcqData, setMcqData] = useState<MCQData | null>(null)

  // Determine if this is an MCQ question
  const isMCQ = problemStatementData?.question_type === 'mcq'

  // Track whether MCQ mode is active (either automatically or manually set)
  const [mcqModeActive, setMcqModeActive] = useState<boolean>(false)

  // Update MCQ mode when question type changes
  useEffect(() => {
    if (isMCQ) {
      setMcqModeActive(true)
    }
  }, [isMCQ])

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)

  const [isResetting, setIsResetting] = useState(false)

  interface Screenshot {
    id: string
    path: string
    preview: string
    timestamp: number
  }

  const [extraScreenshots, setExtraScreenshots] = useState<Screenshot[]>([])

  useEffect(() => {
    const fetchScreenshots = async () => {
      try {
        const existing = await window.electronAPI.getScreenshots()
        console.log("Raw screenshot data:", existing)
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        console.log("Processed screenshots:", screenshots)
        setExtraScreenshots(screenshots)
      } catch (error) {
        console.error("Error loading extra screenshots:", error)
        setExtraScreenshots([])
      }
    }

    fetchScreenshots()
  }, [solutionData])

  const { showToast } = useToast()

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(async () => {
        try {
          const existing = await window.electronAPI.getScreenshots()
          const screenshots = (Array.isArray(existing) ? existing : []).map(
            (p) => ({
              id: p.path,
              path: p.path,
              preview: p.preview,
              timestamp: Date.now()
            })
          )
          setExtraScreenshots(screenshots)
        } catch (error) {
          console.error("Error loading extra screenshots:", error)
        }
      }),
      window.electronAPI.onResetView(() => {
        // Set resetting state first
        setIsResetting(true)

        // Remove queries
        queryClient.removeQueries({
          queryKey: ["solution"]
        })
        queryClient.removeQueries({
          queryKey: ["new_solution"]
        })

        // Reset screenshots
        setExtraScreenshots([])

        // Reset MCQ mode
        setMcqModeActive(false)

        // After a small delay, clear the resetting state
        setTimeout(() => {
          setIsResetting(false)
        }, 0)
      }),
      window.electronAPI.onSolutionStart(() => {
        // Every time processing starts, reset relevant states
        setSolutionData(null)
        setThoughtsData(null)
        setTimeComplexityData(null)
        setSpaceComplexityData(null)
      }),
      window.electronAPI.onProblemExtracted((data: ProblemStatementData) => {
        console.log("Problem extracted event received:", data);
        // Set the problem statement data in the query cache
        queryClient.setQueryData(["problem_statement"], data);
        // Explicitly update the component state to ensure UI updates
        setProblemStatementData(data);

        // If this is an MCQ question, activate MCQ mode
        if (data.question_type === 'mcq') {
          setMcqModeActive(true);
        }
      }),
      //if there was an error processing the initial solution
      window.electronAPI.onSolutionError((error: string) => {
        showToast("Processing Failed", error, "error")
        // Reset solutions in the cache (even though this shouldn't ever happen) and complexities to previous states
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string[]
          time_complexity: string
          space_complexity: string
        } | null
        if (!solution) {
          setView("queue")
        }
        setSolutionData(solution?.code || null)
        setThoughtsData(solution?.thoughts || null)
        setTimeComplexityData(solution?.time_complexity || null)
        setSpaceComplexityData(solution?.space_complexity || null)
        console.error("Processing error:", error)
      }),
      //when the initial solution is generated, we'll set the solution data to that
      window.electronAPI.onSolutionSuccess((data: any) => {
        if (!data) {
          console.warn("Received empty or invalid solution data")
          return
        }
        console.log({ data })
        const solutionData = {
          code: data.code,
          thoughts: data.thoughts,
          time_complexity: data.time_complexity,
          space_complexity: data.space_complexity
        }

        // Log solution data for debugging
        console.log("Setting solution data:", solutionData)

        // Store in query cache
        queryClient.setQueryData(["solution"], solutionData)

        // Update state variables directly
        setSolutionData(solutionData.code || null)
        setThoughtsData(solutionData.thoughts || null)
        setTimeComplexityData(solutionData.time_complexity || null)
        setSpaceComplexityData(solutionData.space_complexity || null)

        // Check if this is an MCQ solution
        const problemData = queryClient.getQueryData(["problem_statement"]) as ExtendedProblemStatementData | null;
        if (problemData?.question_type === 'mcq') {
          try {
            // Try to parse MCQ data from the solution
            const parsedData = JSON.parse(solutionData.code || '{}');
            setMcqData(parsedData);
            setMcqModeActive(true); // Activate MCQ mode
          } catch (error) {
            console.error("Error parsing MCQ data:", error);
          }
        }

        // Force a re-render after a short delay (helps with Qwen model)
        setTimeout(() => {
          if (solutionData && !solutionData.code) {
            console.log("Re-applying solution data after timeout")
            setSolutionData(solutionData.code || null)
          }
        }, 100)

        // Fetch latest screenshots when solution is successful
        const fetchScreenshots = async () => {
          try {
            const existing = await window.electronAPI.getScreenshots()
            const screenshots =
              existing.previews?.map((p: any) => ({
                id: p.path,
                path: p.path,
                preview: p.preview,
                timestamp: Date.now()
              })) || []
            setExtraScreenshots(screenshots)
          } catch (error) {
            console.error("Error loading extra screenshots:", error)
            setExtraScreenshots([])
          }
        }
        fetchScreenshots()
      }),
      // Handle MCQ mode toggle from keyboard shortcut
      window.electronAPI.onToggleMCQMode(() => {
        console.log("Toggle MCQ mode event received");

        // If we have MCQ data, toggle MCQ mode
        if (mcqData) {
          setMcqModeActive(prev => !prev);
          showToast(
            "MCQ Mode",
            mcqModeActive ? "Switched to code view" : "Switched to MCQ view",
            "neutral"
          );
        } else {
          // Try to parse MCQ data from solution if available
          try {
            if (solutionData) {
              const parsedData = JSON.parse(solutionData);
              if (parsedData.question && parsedData.options && parsedData.correct_answer) {
                setMcqData(parsedData);
                setMcqModeActive(true);
                showToast("MCQ Mode", "Switched to MCQ view", "neutral");
              } else {
                showToast("MCQ Mode", "No MCQ data available", "error");
              }
            } else {
              showToast("MCQ Mode", "No solution data available", "error");
            }
          } catch (error) {
            console.error("Error parsing MCQ data:", error);
            showToast("MCQ Mode", "Failed to parse MCQ data", "error");
          }
        }
      }),

      //########################################################
      //DEBUG EVENTS
      //########################################################
      window.electronAPI.onDebugStart(() => {
        //we'll set the debug processing state to true and use that to render a little loader
        setDebugProcessing(true)
      }),
      //the first time debugging works, we'll set the view to debug and populate the cache with the data
      window.electronAPI.onDebugSuccess((data: any) => {
        queryClient.setQueryData(["new_solution"], data)
        setDebugProcessing(false)
      }),
      //when there was an error in the initial debugging, we'll show a toast and stop the little generating pulsing thing.
      window.electronAPI.onDebugError(() => {
        showToast(
          "Processing Failed",
          "There was an error debugging your code.",
          "error"
        )
        setDebugProcessing(false)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no extra screenshots to process.",
          "neutral"
        )
      }),
      // Removed out of credits handler - unlimited credits in this version
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight])

  useEffect(() => {
    // Initial data load
    const problemData = queryClient.getQueryData(["problem_statement"]) as ExtendedProblemStatementData | null;
    setProblemStatementData(problemData);

    const solutionData = queryClient.getQueryData(["solution"]) as {
      code: string
      thoughts: string[]
      time_complexity: string
      space_complexity: string
    } | null;

    if (solutionData) {
      console.log("Setting solution data from cache:", solutionData);
      setSolutionData(solutionData.code ?? null);
      setThoughtsData(solutionData.thoughts ?? null);
      setTimeComplexityData(solutionData.time_complexity ?? null);
      setSpaceComplexityData(solutionData.space_complexity ?? null);

      // Try to parse MCQ data if this is an MCQ question
      if (problemData?.question_type === 'mcq') {
        try {
          console.log("Processing MCQ data in Solutions component");

          // First check if we have MCQ data in the problem statement
          if (problemData.mcq_data) {
            console.log("Using MCQ data from problem statement:", problemData.mcq_data);
            setMcqData(problemData.mcq_data);
            setMcqModeActive(true); // Activate MCQ mode
          } else {
            // Otherwise try to parse it from the solution code
            console.log("Attempting to parse MCQ data from solution code:", solutionData.code);

            // Try multiple approaches to extract MCQ data
            let mcqDataExtracted = false;

            // Approach 1: Direct JSON parsing
            try {
              const parsedData = JSON.parse(solutionData.code);
              console.log("Successfully parsed MCQ data:", parsedData);

              // Validate the parsed data has the required fields
              if (parsedData.question && parsedData.options && parsedData.correct_answer) {
                setMcqData(parsedData);
                setMcqModeActive(true); // Activate MCQ mode
                mcqDataExtracted = true;
              } else {
                console.warn("Parsed data is missing required MCQ fields");
              }
            } catch (parseError) {
              console.error("JSON parse error for MCQ data:", parseError);
            }

            // Approach 2: Extract JSON object from text
            if (!mcqDataExtracted) {
              try {
                const jsonMatch = solutionData.code.match(/(\{[\s\S]*\})/);
                if (jsonMatch && jsonMatch[1]) {
                  const jsonStr = jsonMatch[1];
                  console.log("Found JSON-like string in solution:", jsonStr);

                  const parsedData = JSON.parse(jsonStr);
                  if (parsedData.question && parsedData.options && parsedData.correct_answer) {
                    console.log("Successfully parsed MCQ data from JSON substring:", parsedData);
                    setMcqData(parsedData);
                    setMcqModeActive(true); // Activate MCQ mode
                    mcqDataExtracted = true;
                  }
                }
              } catch (jsonExtractError) {
                console.error("Error extracting JSON from solution:", jsonExtractError);
              }
            }

            // Approach 3: Regex extraction as last resort
            if (!mcqDataExtracted) {
              console.log("Attempting to extract MCQ data using regex");
              const questionMatch = solutionData.code.match(/"question":\s*"([^"]+)"/);
              const optionsMatch = solutionData.code.match(/"options":\s*(\[[^\]]+\])/);
              const correctAnswerMatch = solutionData.code.match(/"correct_answer":\s*"([^"]+)"/);
              const explanationMatch = solutionData.code.match(/"explanation":\s*"([^"]+)"/);

              if (questionMatch && optionsMatch && correctAnswerMatch && explanationMatch) {
                try {
                  const options = JSON.parse(optionsMatch[1]);
                  const extractedData = {
                    question: questionMatch[1],
                    options: options,
                    correct_answer: correctAnswerMatch[1],
                    explanation: explanationMatch[1]
                  };
                  console.log("Extracted MCQ data using regex:", extractedData);
                  setMcqData(extractedData);
                  setMcqModeActive(true); // Activate MCQ mode
                  mcqDataExtracted = true;
                } catch (regexError) {
                  console.error("Failed to extract MCQ data using regex:", regexError);
                }
              }
            }

            // If we still couldn't extract MCQ data, log a warning
            if (!mcqDataExtracted) {
              console.warn("Failed to extract MCQ data from solution using all approaches");
            }
          }
        } catch (error) {
          console.error("Error processing MCQ data:", error);
        }
      }
    } else {
      console.log("No solution data in cache");
    }

    // Set up a periodic check for solution data in case it's not immediately available
    // This helps with the Qwen model integration
    const checkInterval = setInterval(() => {
      const cachedSolution = queryClient.getQueryData(["solution"]) as {
        code: string
        thoughts: string[]
        time_complexity: string
        space_complexity: string
      } | null;

      if (cachedSolution && !solutionData) {
        console.log("Found solution data in cache during interval check:", cachedSolution);
        setSolutionData(cachedSolution.code ?? null);
        setThoughtsData(cachedSolution.thoughts ?? null);
        setTimeComplexityData(cachedSolution.time_complexity ?? null);
        setSpaceComplexityData(cachedSolution.space_complexity ?? null);

        // Check if this is an MCQ solution
        try {
          const problemData = queryClient.getQueryData(["problem_statement"]) as ExtendedProblemStatementData | null;
          if (problemData?.question_type === 'mcq') {
            // Try to parse MCQ data from the solution
            try {
              const parsedData = JSON.parse(cachedSolution.code);
              setMcqData(parsedData);
              setMcqModeActive(true); // Activate MCQ mode
            } catch (error) {
              console.error("Error parsing MCQ data during interval check:", error);
            }
          }
        } catch (error) {
          console.error("Error checking for MCQ data during interval check:", error);
        }
      }
    }, 500);

    // Clean up interval
    return () => clearInterval(checkInterval);
  }, [queryClient]);

  // Subscribe to query cache changes
  useEffect(() => {
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.query.queryKey[0] === "problem_statement") {
        const newProblemData = queryClient.getQueryData(["problem_statement"]) as ExtendedProblemStatementData | null;
        console.log("Problem statement updated:", newProblemData);
        setProblemStatementData(newProblemData);

        // Check if this is an MCQ and update MCQ data if available
        if (newProblemData?.question_type === 'mcq') {
          setMcqModeActive(true); // Activate MCQ mode
          if (newProblemData.mcq_data) {
            setMcqData(newProblemData.mcq_data);
          }
        }
      }
      if (event?.query.queryKey[0] === "solution") {
        const newSolution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string[]
          time_complexity: string
          space_complexity: string
        } | null;

        console.log("Solution updated:", newSolution);
        setSolutionData(newSolution?.code ?? null);
        setThoughtsData(newSolution?.thoughts ?? null);
        setTimeComplexityData(newSolution?.time_complexity ?? null);
        setSpaceComplexityData(newSolution?.space_complexity ?? null);

        // Try to parse MCQ data from solution if this is an MCQ
        const problemData = queryClient.getQueryData(["problem_statement"]) as ExtendedProblemStatementData | null;
        if (problemData?.question_type === 'mcq' && newSolution?.code) {
          setMcqModeActive(true); // Activate MCQ mode
          try {
            console.log("Processing updated MCQ data in Solutions component");

            // If we don't already have MCQ data from the problem statement
            if (!mcqData) {
              console.log("Attempting to parse updated MCQ data from solution code:", newSolution.code);
              try {
                const parsedData = JSON.parse(newSolution.code);
                console.log("Successfully parsed updated MCQ data:", parsedData);
                setMcqData(parsedData);
              } catch (parseError) {
                console.error("JSON parse error for updated MCQ data:", parseError);

                // Try to extract MCQ data using regex as a fallback
                console.log("Attempting to extract updated MCQ data using regex");
                const questionMatch = newSolution.code.match(/"question":\s*"([^"]+)"/);
                const optionsMatch = newSolution.code.match(/"options":\s*(\[[^\]]+\])/);
                const correctAnswerMatch = newSolution.code.match(/"correct_answer":\s*"([^"]+)"/);
                const explanationMatch = newSolution.code.match(/"explanation":\s*"([^"]+)"/);

                if (questionMatch && optionsMatch && correctAnswerMatch && explanationMatch) {
                  try {
                    const options = JSON.parse(optionsMatch[1]);
                    const extractedData = {
                      question: questionMatch[1],
                      options: options,
                      correct_answer: correctAnswerMatch[1],
                      explanation: explanationMatch[1]
                    };
                    console.log("Extracted updated MCQ data using regex:", extractedData);
                    setMcqData(extractedData);
                  } catch (regexError) {
                    console.error("Failed to extract updated MCQ data using regex:", regexError);
                  }
                }
              }
            }
          } catch (error) {
            console.error("Error processing updated MCQ data from solution:", error);
          }
        }
      }
    });

    return () => unsubscribe();
  }, [queryClient, mcqData]);

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const handleDeleteExtraScreenshot = async (index: number) => {
    const screenshotToDelete = extraScreenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        // Fetch and update screenshots after successful deletion
        const existing = await window.electronAPI.getScreenshots()
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        setExtraScreenshots(screenshots)
      } else {
        console.error("Failed to delete extra screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot", "error")
      }
    } catch (error) {
      console.error("Error deleting extra screenshot:", error)
      showToast("Error", "Failed to delete the screenshot", "error")
    }
  }

  return (
    <>
      {!isResetting && queryClient.getQueryData(["new_solution"]) ? (
        <Debug
          isProcessing={debugProcessing}
          setIsProcessing={setDebugProcessing}
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : (
        <div ref={contentRef} className="relative">
          <div className="space-y-3 px-4 py-3">
          {/* Conditionally render the screenshot queue if solutionData is available */}
          {solutionData && (
            <div className="bg-transparent w-fit">
              <div className="pb-3">
                <div className="space-y-3 w-fit">
                  <ScreenshotQueue
                    isLoading={debugProcessing}
                    screenshots={extraScreenshots}
                    onDeleteScreenshot={handleDeleteExtraScreenshot}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Navbar of commands with the SolutionsHelper */}
          <SolutionCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            isProcessing={!problemStatementData || !solutionData}
            extraScreenshots={extraScreenshots}
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />

          {/* Main Content - Modified width constraints */}
          <div className="w-full text-sm text-black bg-black/60 rounded-md">
            <div className="rounded-lg overflow-hidden">
              <div className="px-4 py-3 space-y-4 max-w-full">
                {!solutionData && (
                  <>
                    <ContentSection
                      title="Problem Statement"
                      content={problemStatementData?.problem_statement}
                      isLoading={!problemStatementData}
                    />
                    {problemStatementData && (
                      <>
                        {/* Check if this is an MCQ question */}
                        {problemStatementData.question_type === 'mcq' ? (
                          <div className="mt-4">
                            <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
                              Analyzing MCQ question...
                            </p>
                            {/* Add debug info */}
                            {process.env.NODE_ENV === 'development' && (
                              <div className="mt-2 text-xs text-gray-500">
                                MCQ question detected. Question type: {problemStatementData.question_type}
                                <br />
                                MCQ data available: {problemStatementData.mcq_data ? 'Yes' : 'No'}
                                <br />
                                MCQ mode active: {mcqModeActive ? 'Yes' : 'No'}
                              </div>
                            )}

                            {/* If we have MCQ data in the problem statement, display it immediately */}
                            {problemStatementData.mcq_data && (
                              <div className="mt-4">
                                <MCQSection
                                  title="Multiple Choice Question"
                                  mcqData={problemStatementData.mcq_data}
                                  isLoading={false}
                                />
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="mt-4 flex">
                            <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
                              Generating solutions...
                            </p>
                          </div>
                        )}
                      </>
                    )}
                    {/* Debug message is hidden in production */}
                    {process.env.NODE_ENV === 'development' && (
                      <div className="mt-2 text-xs text-gray-500">
                        {queryClient.getQueryData(["solution"]) ? "Solution data is in cache" : "No solution data in cache yet"}
                      </div>
                    )}
                  </>
                )}

                {solutionData && (
                  <>
                    <ContentSection
                      title={`My Thoughts (${COMMAND_KEY} + Arrow keys to scroll)`}
                      content={
                        thoughtsData && (
                          <div className="space-y-3">
                            <div className="space-y-1">
                              {thoughtsData.map((thought, index) => (
                                <div
                                  key={index}
                                  className="flex items-start gap-2"
                                >
                                  <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
                                  <div>{thought}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )
                      }
                      isLoading={!thoughtsData}
                    />

                    {/* Conditionally render either MCQ or coding solution */}
                    {console.log("Rendering solution section, isMCQ:", isMCQ, "mcqData:", mcqData, "mcqModeActive:", mcqModeActive, "problemStatementData:", problemStatementData)}

                    {/* Add debug info in development mode */}
                    {process.env.NODE_ENV === 'development' && (
                      <div className="mt-2 mb-4 p-2 bg-black/30 rounded text-xs text-gray-400">
                        <div>MCQ Debug Info:</div>
                        <div>isMCQ: {isMCQ ? 'Yes' : 'No'}</div>
                        <div>mcqModeActive: {mcqModeActive ? 'Yes' : 'No'}</div>
                        <div>mcqData available: {mcqData ? 'Yes' : 'No'}</div>
                        <div>solutionData available: {solutionData ? 'Yes' : 'No'}</div>
                        {mcqData && (
                          <div>
                            <div>MCQ Data:</div>
                            <pre className="text-xs overflow-auto max-h-20">
                              {JSON.stringify(mcqData, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Show MCQ section if it's an MCQ question and we have MCQ data */}
                    {(isMCQ || mcqModeActive) && mcqData ? (
                      <>
                        {console.log("Rendering MCQ section with data:", mcqData)}
                        <MCQSection
                          title="Multiple Choice Question"
                          mcqData={mcqData}
                          isLoading={!mcqData}
                        />

                        {/* Add a button to toggle back to code view if needed */}
                        {solutionData && (
                          <div className="mt-4">
                            <button
                              onClick={() => setMcqModeActive(false)}
                              className="px-3 py-1.5 text-xs bg-white/10 hover:bg-white/20 text-white rounded transition-colors"
                            >
                              View as Code
                            </button>
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <SolutionSection
                          title="Solution"
                          content={solutionData}
                          isLoading={!solutionData}
                          currentLanguage={currentLanguage}
                        />

                        <ComplexitySection
                          timeComplexity={timeComplexityData}
                          spaceComplexity={spaceComplexityData}
                          isLoading={!timeComplexityData || !spaceComplexityData}
                        />

                        {/* Add a button to toggle to MCQ view if MCQ data is available */}
                        {mcqData && (
                          <div className="mt-4">
                            <button
                              onClick={() => setMcqModeActive(true)}
                              className="px-3 py-1.5 text-xs bg-white/10 hover:bg-white/20 text-white rounded transition-colors"
                            >
                              View as MCQ
                            </button>
                          </div>
                        )}
                      </>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      )}
    </>
  )
}

export default Solutions
