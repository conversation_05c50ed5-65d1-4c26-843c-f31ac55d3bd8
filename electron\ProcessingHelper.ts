// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, BrowserWindow, dialog } from "electron"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk';

// Interface for Gemini API requests
interface GeminiMessage {
  role: string;
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    }
  }>;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
}
interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    source?: {
      type: 'base64';
      media_type: string;
      data: string;
    };
  }>;
}
export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null
  private openrouterApiKey: string | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()

    // Initialize AI client based on config
    this.initializeAIClient();

    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient();
    });
  }

  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient(): void {
    try {
      const config = configHelper.loadConfig();

      // Reset all clients first
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
      this.openrouterApiKey = null;

      if (config.apiProvider === "openai") {
        if (config.apiKey) {
          this.openaiClient = new OpenAI({
            apiKey: config.apiKey,
            timeout: 60000, // 60 second timeout
            maxRetries: 2   // Retry up to 2 times
          });
          console.log("OpenAI client initialized successfully");
        } else {
          console.warn("No API key available, OpenAI client not initialized");
        }
      } else if (config.apiProvider === "gemini"){
        // Gemini client initialization
        if (config.apiKey) {
          this.geminiApiKey = config.apiKey;
          console.log("Gemini API key set successfully");
        } else {
          console.warn("No API key available, Gemini client not initialized");
        }
      } else if (config.apiProvider === "anthropic") {
        if (config.apiKey) {
          this.anthropicClient = new Anthropic({
            apiKey: config.apiKey,
            timeout: 60000,
            maxRetries: 2
          });
          console.log("Anthropic client initialized successfully");
        } else {
          console.warn("No API key available, Anthropic client not initialized");
        }
      } else if (config.apiProvider === "openrouter") {
        // For OpenRouter, we'll use Gemini for image processing and OpenRouter for text
        if (config.apiKey) {
          this.openrouterApiKey = config.apiKey;

          // For image support, we need to initialize Gemini as well
          if (config.extractionModel === "gemini-2.0-flash" || config.debuggingModel === "gemini-2.0-flash") {
            // Use the stored Gemini API key
            if (config.geminiApiKey) {
              this.geminiApiKey = config.geminiApiKey;
              console.log("Using stored Gemini API key for image processing");
            } else {
              console.warn("No Gemini API key available for image processing");
            }
          }

          console.log("OpenRouter API key set successfully");
        } else {
          console.warn("No API key available, OpenRouter client not initialized");
        }
      }
    } catch (error) {
      console.error("Failed to initialize AI client:", error);
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
      this.openrouterApiKey = null;
    }
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getCredits(): Promise<number> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return 999 // Unlimited credits in this version

    try {
      await this.waitForInitialization(mainWindow)
      return 999 // Always return sufficient credits to work
    } catch (error) {
      console.error("Error getting credits:", error)
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig();
      if (config.language) {
        return config.language;
      }

      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language;
          }
        } catch (err) {
          console.warn("Could not get language from window", err);
        }
      }

      // Default fallback
      return "python";
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();

    // First verify we have a valid AI client
    if (config.apiProvider === "openai" && !this.openaiClient) {
      this.initializeAIClient();

      if (!this.openaiClient) {
        console.error("OpenAI client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "gemini" && !this.geminiApiKey) {
      this.initializeAIClient();

      if (!this.geminiApiKey) {
        console.error("Gemini API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "anthropic" && !this.anthropicClient) {
      // Add check for Anthropic client
      this.initializeAIClient();

      if (!this.anthropicClient) {
        console.error("Anthropic client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "openrouter") {
      // Add check for OpenRouter client
      this.initializeAIClient();

      if (!this.openrouterApiKey) {
        console.error("OpenRouter API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }

      // For image processing with OpenRouter, we need Gemini
      if ((config.extractionModel === "gemini-2.0-flash" || config.debuggingModel === "gemini-2.0-flash") && !this.geminiApiKey) {
        console.error("Gemini API key not available for image processing with OpenRouter");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID,
          { message: "Gemini API key is required for image processing. Please add it in settings." }
        );
        return;
      }
    }

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)

      // Check if the queue is empty
      if (!screenshotQueue || screenshotQueue.length === 0) {
        console.log("No screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter(path => fs.existsSync(path));
      if (existingScreenshots.length === 0) {
        console.log("Screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map(async (path) => {
            try {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);

        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data");
        }

        const result = await this.processScreenshotsHelper(validScreenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key") || result.error?.includes("OpenAI") || result.error?.includes("Gemini")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")

        // Send solution success event
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )

        // Add a small delay before changing view to ensure UI updates properly
        // This helps especially with the Qwen model
        console.log("Adding delay before changing view to ensure UI updates properly")
        setTimeout(() => {
          this.deps.setView("solutions")
        }, 200)
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)

      // Check if the extra queue is empty
      if (!extraScreenshotQueue || extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);

        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter(path => fs.existsSync(path));
      if (existingExtraScreenshots.length === 0) {
        console.log("Extra screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];

        const screenshots = await Promise.all(
          allPaths.map(async (path) => {
            try {
              if (!fs.existsSync(path)) {
                console.warn(`Screenshot file does not exist: ${path}`);
                return null;
              }

              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);

        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data for debugging");
        }

        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // Step 1: Extract problem info using AI Vision API (OpenAI or Gemini)
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Analyzing problem from screenshots...",
          progress: 20
        });
      }

      let problemInfo;

      if (config.apiProvider === "openai") {
        // Verify OpenAI client
        if (!this.openaiClient) {
          this.initializeAIClient(); // Try to reinitialize

          if (!this.openaiClient) {
            return {
              success: false,
              error: "OpenAI API key not configured or invalid. Please check your settings."
            };
          }
        }

        // Use OpenAI for processing
        const messages = [
          {
            role: "system" as const,
            content: "You are a coding challenge and MCQ interpreter. Analyze the screenshot and determine if it contains a coding problem or a multiple-choice question (MCQ). Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nIMPORTANT FOR MCQs: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text."
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `Extract the coding problem details from these screenshots. Return in JSON format. Preferred coding language we gonna use for this problem is ${language}.`
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        // Send to OpenAI Vision API
        const extractionResponse = await this.openaiClient.chat.completions.create({
          model: config.extractionModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });

        // Parse the response
        try {
          const responseText = extractionResponse.choices[0].message.content;
          // Handle when OpenAI might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error parsing OpenAI response:", error);
          return {
            success: false,
            error: "Failed to parse problem information. Please try again or use clearer screenshots."
          };
        }
      } else if (config.apiProvider === "gemini")  {
        // Use Gemini API
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          // Create Gemini message structure
          const geminiMessages: GeminiMessage[] = [
            {
              role: "user",
              parts: [
                {
                  text: `You are a coding challenge and MCQ interpreter. Analyze the screenshot and determine if it contains a coding problem or a multiple-choice question (MCQ). Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nIMPORTANT FOR MCQs: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text. Preferred coding language we gonna use for this problem is ${language}.`
                },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.extractionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          const responseText = responseData.candidates[0].content.parts[0].text;

          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error using Gemini API:", error);
          return {
            success: false,
            error: "Failed to process with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "openrouter") {
        // For OpenRouter, we use Gemini for image processing (extraction)
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured for image processing. Please add a Gemini API key in settings."
          };
        }

        // Use the same Gemini code for extraction as above
        try {
          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: `You are a coding challenge and MCQ interpreter. Analyze the screenshot and determine if it contains a coding problem or a multiple-choice question (MCQ). Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nIMPORTANT FOR MCQs: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text. Preferred coding language we gonna use for this problem is ${language}.`
                },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          const responseText = responseData.candidates[0].content.parts[0].text;

          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error using Gemini API for extraction with OpenRouter:", error);
          return {
            success: false,
            error: "Failed to process with Gemini API. Please check your Gemini API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `Analyze the screenshot and determine if it contains a coding problem or a multiple-choice question (MCQ). Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nIMPORTANT FOR MCQs: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text. Preferred coding language is ${language}.`
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          const response = await this.anthropicClient.messages.create({
            model: config.extractionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          const responseText = (response.content[0] as { type: 'text', text: string }).text;
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error: any) {
          console.error("Error using Anthropic API:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process with Anthropic API. Please check your API key or try again later."
          };
        }
      }

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        });
      }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo);

      // Send first success event
      if (mainWindow) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Add a delay to allow the UI to update and show the problem statement
        // before starting to generate the solution
        console.log("Adding delay before generating solution to allow UI to update");
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Update progress status to indicate we're moving to solution generation
        mainWindow.webContents.send("processing-status", {
          message: "Problem extracted successfully. Starting solution generation...",
          progress: 50
        });

        // Generate solutions after successful extraction
        const solutionsResult = await this.generateSolutionsHelper(signal);
        if (solutionsResult.success) {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();

          // Final progress update
          mainWindow.webContents.send("processing-status", {
            message: "Solution generated successfully",
            progress: 100
          });

          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch (error: any) {
      // If the request was cancelled, don't retry
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      // Handle OpenAI API errors specifically
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if (error?.response?.status === 500) {
        return {
          success: false,
          error: "OpenAI server error. Please try again later."
        };
      }

      console.error("API Error Details:", error);
      return {
        success: false,
        error: error.message || "Failed to process screenshots. Please try again."
      };
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Check if this is an MCQ or coding question
      const isMCQ = problemInfo.question_type === 'mcq';

      // Update progress status with appropriate message
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: isMCQ
            ? "Analyzing MCQ and preparing answer..."
            : "Creating optimal solution with detailed explanations...",
          progress: 60
        });
      }

      // If MCQ support is disabled, treat all questions as coding questions
      const mcqSupport = config.mcqSupport !== undefined ? config.mcqSupport : true;

      // Create prompt based on question type
      let promptText;

      if (isMCQ && mcqSupport) {
        console.log("🔄 MCQ PROCESSING: Processing MCQ question:", problemInfo);
        console.log("🔄 MCQ PROCESSING: API Provider:", config.apiProvider);

        // Check if we have complete MCQ data (with correct_answer and explanation)
        const hasCompleteData = problemInfo.mcq_data &&
                               problemInfo.mcq_data.correct_answer &&
                               problemInfo.mcq_data.explanation;

        // For OpenRouter, always route MCQs to QWen model for processing, even if partial data exists
        if (config.apiProvider === "openrouter") {
          console.log("🔄 MCQ PROCESSING: OpenRouter detected - routing MCQ to QWen model");
          console.log("🔄 MCQ PROCESSING: MCQ data status:", hasCompleteData ? "Complete" : "Incomplete/Missing");

          // Use existing MCQ data if available, otherwise extract from problem statement
          const mcqQuestion = problemInfo.mcq_data?.question || problemInfo.problem_statement;
          const mcqOptions = problemInfo.mcq_data?.options || [];

          promptText = `
Analyze this multiple-choice question and provide the correct answer with explanation:

QUESTION:
${mcqQuestion}

${mcqOptions.length > 0 ? `OPTIONS:
${mcqOptions.map((opt: any) => `${opt.id}. ${opt.text}`).join('\n')}` : ''}

Please provide:
1. The correct answer option (A, B, C, or D)
2. A detailed explanation of why this is the correct answer
3. Brief explanations of why the other options are incorrect

Format your response as a JSON object with these fields:
{
  "question": "the full question text",
  "options": [{"id": "A", "text": "option text"}, ...],
  "correct_answer": "A",
  "explanation": "detailed explanation of the correct answer"
}

IMPORTANT: Your entire response must be valid JSON that can be parsed with JSON.parse(). Do not include any text before or after the JSON object.
`;
        } else {
          // For non-OpenRouter providers, only return directly if we have complete MCQ data
          if (hasCompleteData) {
            console.log("🔄 MCQ PROCESSING: Complete MCQ data found, returning directly:", problemInfo.mcq_data);

            // Ensure the main window is notified about the MCQ data
            if (mainWindow) {
              mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, {
                ...problemInfo,
                question_type: 'mcq',
                mcq_data: problemInfo.mcq_data
              });
            }

            return {
              success: true,
              data: {
                solution: JSON.stringify(problemInfo.mcq_data),
                thoughts: ["This is a multiple-choice question.", "The correct answer has been identified."],
                time_complexity: "Not applicable for MCQs",
                space_complexity: "Not applicable for MCQs"
              }
            };
          } else {
            console.log("🔄 MCQ PROCESSING: Incomplete/No MCQ data found, generating it now");
            console.log("🔄 MCQ PROCESSING: Will route to", config.apiProvider);

            // Use existing MCQ data if available, otherwise extract from problem statement
            const mcqQuestion = problemInfo.mcq_data?.question || problemInfo.problem_statement;
            const mcqOptions = problemInfo.mcq_data?.options || [];

            promptText = `
Analyze this multiple-choice question and provide the correct answer with explanation:

QUESTION:
${mcqQuestion}

${mcqOptions.length > 0 ? `OPTIONS:
${mcqOptions.map((opt: any) => `${opt.id}. ${opt.text}`).join('\n')}` : ''}

Please provide:
1. The correct answer option (A, B, C, or D)
2. A detailed explanation of why this is the correct answer
3. Brief explanations of why the other options are incorrect

Format your response as a JSON object with these fields:
{
  "question": "the full question text",
  "options": [{"id": "A", "text": "option text"}, ...],
  "correct_answer": "A",
  "explanation": "detailed explanation of the correct answer"
}

IMPORTANT: Your entire response must be valid JSON that can be parsed with JSON.parse(). Do not include any text before or after the JSON object.
`;
          }
        }
      } else {
        // For coding problems, use the original prompt
        const defaultFunction = problemInfo.default_function_signature || "";
        const hasDefaultFunction = defaultFunction.trim().length > 0;

        promptText = `
Generate a detailed solution for the following coding problem:

PROBLEM STATEMENT:
${problemInfo.problem_statement}

CONSTRAINTS:
${problemInfo.constraints || "No specific constraints provided."}

EXAMPLE INPUT:
${problemInfo.example_input || "No example input provided."}

EXAMPLE OUTPUT:
${problemInfo.example_output || "No example output provided."}

${hasDefaultFunction ? `DEFAULT FUNCTION SIGNATURE:
${defaultFunction}

IMPORTANT: Use the exact function signature provided above. Maintain the exact function name, parameters, and return type. Do not modify the function signature in any way.` : ""}

LANGUAGE: ${language}

I need the response in the following format:
1. Code: A clean, optimized implementation in ${language}${hasDefaultFunction ? " using the provided function signature" : ""}
2. Your Thoughts: A list of key insights and reasoning behind your approach
3. Time complexity: O(X) with a detailed explanation (at least 2 sentences)
4. Space complexity: O(X) with a detailed explanation (at least 2 sentences)

${hasDefaultFunction ? `CRITICAL INSTRUCTIONS:
- You MUST use the exact function signature provided above
- Do NOT change the function name, parameter names, parameter types, or return type
- Only implement the function body
- If the problem provides a class structure, maintain it exactly as given` : `INSTRUCTIONS:
- Since no default function signature is provided, create a complete standalone solution
- Include proper main function or test cases as appropriate for ${language}`}

For complexity explanations, please be thorough. For example: "Time complexity: O(n) because we iterate through the array only once. This is optimal as we need to examine each element at least once to find the solution." or "Space complexity: O(n) because in the worst case, we store all elements in the hashmap. The additional space scales linearly with the input size."

Your solution should be efficient, well-commented, and handle edge cases.
`;
      }

      let responseContent;

      if (config.apiProvider === "openai") {
        // OpenAI processing
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        // Send to OpenAI API
        const solutionResponse = await this.openaiClient.chat.completions.create({
          model: config.solutionModel || "gpt-4o",
          messages: [
            { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
            { role: "user", content: promptText }
          ],
          max_tokens: 4000,
          temperature: 0.2
        });

        responseContent = solutionResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        // Gemini processing
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.solutionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          responseContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for solution:", error);
          return {
            success: false,
            error: "Failed to generate solution with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "openrouter") {
        // OpenRouter processing
        if (!this.openrouterApiKey) {
          return {
            success: false,
            error: "OpenRouter API key not configured. Please check your settings."
          };
        }

        // Create OpenRouter request
        // Make sure the model ID is correctly formatted for OpenRouter
        let openRouterModel = config.openrouterModel || "qwen/qwq-32b:free";

        // OpenRouter sometimes requires model IDs in a specific format
        console.log("Using model with OpenRouter:", openRouterModel);

        console.log("Making OpenRouter API request with model:", openRouterModel);

        // Update progress for the user
        if (mainWindow) {
          mainWindow.webContents.send("processing-status", {
            message: "Generating solution with OpenRouter (Qwen model)...",
            progress: 60
          });
        }

        // Adjust parameters based on model type and question complexity
        let maxTokens = 4000;
        let timeoutMs = 120000; // 2 minutes default
        let systemPrompt = "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations.";

        try {

          // Special handling for QWen models and complex reasoning
          if (openRouterModel.includes('qwen') || openRouterModel.includes('qwq')) {
            // QwQ models need more tokens and time for reasoning
            if (openRouterModel.includes('qwq')) {
              maxTokens = 20000; // Increase token limit for reasoning models
              timeoutMs = 300000; // 5 minutes for complex reasoning
            } else {
              maxTokens = 10000; // Moderate increase for other Qwen models
              timeoutMs = 180000; // 3 minutes for other Qwen models
            }

            // Enhanced system prompt for reasoning models
            systemPrompt = `You are an expert coding interview assistant with strong reasoning capabilities.
For complex problems, provide your complete thought process step by step, then give the optimal solution with detailed explanations.

CRITICAL: When a default function signature is provided in the problem, you MUST use it exactly as given. Do not modify function names, parameter names, parameter types, or return types. This is essential for platform compatibility (LeetCode, GeeksforGeeks, etc.).

Structure your response clearly with:
1. Problem Analysis
2. Approach/Algorithm
3. Step-by-step reasoning
4. Implementation (respecting any provided function signatures)
5. Complexity analysis
Be thorough but concise in your reasoning.`;
          }

          // For MCQ questions, use different parameters
          if (isMCQ && mcqSupport) {
            console.log("🔍 MCQ PROCESSING: Configuring OpenRouter for MCQ question");
            console.log("🔍 MCQ PROCESSING: Using OpenRouter model:", openRouterModel);
            maxTokens = 10000; // MCQs need less tokens
            timeoutMs = 60000; // 1 minute should be enough for MCQs
            systemPrompt = "You are an expert at analyzing multiple-choice questions. Provide clear, accurate answers with detailed explanations.";
          }

          // Log the API call details
          if (isMCQ && mcqSupport) {
            console.log("🚀 MCQ PROCESSING: Making OpenRouter API call for MCQ");
            console.log("🚀 MCQ PROCESSING: Model:", openRouterModel);
            console.log("🚀 MCQ PROCESSING: Max tokens:", maxTokens);
            console.log("🚀 MCQ PROCESSING: Timeout:", timeoutMs);
          }

          const response = await axios.default.post(
            "https://openrouter.ai/api/v1/chat/completions",
            {
              model: openRouterModel,
              messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: promptText }
              ],
              max_tokens: maxTokens,
              temperature: 0.2,
              // Add streaming support for longer responses
              stream: false
            },
            {
              headers: {
                "Authorization": `Bearer ${this.openrouterApiKey}`,
                "HTTP-Referer": "https://interview-coder.com",
                "X-Title": "Interview Coder"
              },
              signal,
              timeout: timeoutMs
            }
          );

          console.log("OpenRouter API response received:", JSON.stringify(response.data, null, 2));

          if (response.data && response.data.choices && response.data.choices.length > 0) {
            responseContent = response.data.choices[0].message.content;
            console.log("OpenRouter response content extracted successfully");

            // Log MCQ processing success
            if (isMCQ && mcqSupport) {
              console.log("✅ MCQ PROCESSING: OpenRouter API call successful for MCQ");
              console.log("✅ MCQ PROCESSING: Response length:", responseContent?.length || 0);
            }

            // Check if response was truncated (common with reasoning models)
            const choice = response.data.choices[0];
            if (choice.finish_reason === 'length' && openRouterModel.includes('qwen')) {
              console.warn("Response was truncated due to length limit, but continuing with partial response");
              // For QWen models, we'll accept partial responses rather than failing
              // The response should still contain valuable reasoning even if truncated
            }

            // Update progress for the user to indicate completion
            if (mainWindow) {
              mainWindow.webContents.send("processing-status", {
                message: "Solution generated successfully",
                progress: 100
              });
            }
          } else {
            console.error("OpenRouter API response missing expected data structure:", response.data);
            throw new Error("Empty or invalid response from OpenRouter API");
          }
        } catch (error: any) {
          console.error("Error using OpenRouter API for solution:", error);

          // Implement retry logic for specific error types
          if (error?.response?.status === 429 || error?.response?.status === 503 || error?.response?.status === 502) {
            console.log("Rate limit, service unavailable, or bad gateway - implementing retry logic");

            // Wait and retry once for rate limits or service issues
            await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds

            try {
              console.log("Retrying OpenRouter API request...");
              const retryResponse = await axios.default.post(
                "https://openrouter.ai/api/v1/chat/completions",
                {
                  model: openRouterModel,
                  messages: [
                    { role: "system", content: systemPrompt },
                    { role: "user", content: promptText }
                  ],
                  max_tokens: maxTokens,
                  temperature: 0.2,
                  stream: false
                },
                {
                  headers: {
                    "Authorization": `Bearer ${this.openrouterApiKey}`,
                    "HTTP-Referer": "https://interview-coder.com",
                    "X-Title": "Interview Coder"
                  },
                  signal,
                  timeout: timeoutMs
                }
              );

              if (retryResponse.data && retryResponse.data.choices && retryResponse.data.choices.length > 0) {
                responseContent = retryResponse.data.choices[0].message.content;
                console.log("Retry successful");

                // Check for truncation on retry as well
                const retryChoice = retryResponse.data.choices[0];
                if (retryChoice.finish_reason === 'length' && openRouterModel.includes('qwen')) {
                  console.warn("Retry response was also truncated, but continuing with partial response");
                }
              } else {
                throw new Error("Empty response from OpenRouter API on retry");
              }
            } catch (retryError) {
              console.error("Retry also failed:", retryError);
              return {
                success: false,
                error: "Failed to generate solution with OpenRouter API after retry. The service may be temporarily unavailable. Please try again later."
              };
            }
          } else {
            // More detailed error logging for non-retryable errors
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              console.error("OpenRouter API error response data:", error.response.data);
              console.error("OpenRouter API error response status:", error.response.status);
              console.error("OpenRouter API error response headers:", error.response.headers);

              return {
                success: false,
                error: `OpenRouter API error (${error.response.status}): ${JSON.stringify(error.response.data)}`
              };
            } else if (error.request) {
              // The request was made but no response was received
              console.error("OpenRouter API no response received:", error.request);
              return {
                success: false,
                error: "No response received from OpenRouter API. Please check your internet connection."
              };
            } else {
              // Something happened in setting up the request that triggered an Error
              console.error("OpenRouter API request setup error:", error.message);
              return {
                success: false,
                error: `Failed to generate solution with OpenRouter API: ${error.message}`
              };
            }
          }
        }
      } else if (config.apiProvider === "anthropic") {
        // Anthropic processing
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Send to Anthropic API
          const response = await this.anthropicClient.messages.create({
            model: config.solutionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          responseContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for solution:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to generate solution with Anthropic API. Please check your API key or try again later."
          };
        }
      }

      // Check if this is an MCQ response - reuse the problemInfo from above

      if (isMCQ) {
        console.log("Processing MCQ response:", responseContent);

        // Try to parse the response as JSON
        try {
          // First, try to extract JSON if it's wrapped in text
          let jsonContent = responseContent;

          // Look for JSON object pattern
          const jsonMatch = responseContent.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonContent = jsonMatch[0];
          }

          // Parse the JSON
          const mcqData = JSON.parse(jsonContent);
          console.log("Successfully parsed MCQ data:", mcqData);

          // Validate the MCQ data
          if (mcqData.question && mcqData.options && mcqData.correct_answer && mcqData.explanation) {
            // Update the problem info with the MCQ data
            if (this.deps.getMainWindow()) {
              this.deps.getMainWindow().webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, {
                ...problemInfo,
                question_type: 'mcq',
                mcq_data: mcqData
              });
            }

            // Return the MCQ data
            return {
              success: true,
              data: {
                solution: JSON.stringify(mcqData),
                thoughts: ["This is a multiple-choice question.", "The correct answer has been identified."],
                time_complexity: "Not applicable for MCQs",
                space_complexity: "Not applicable for MCQs"
              }
            };
          } else {
            console.error("Invalid MCQ data format:", mcqData);
          }
        } catch (error) {
          console.error("Error parsing MCQ response:", error);
          // Continue with normal processing as fallback
        }
      }

      // Normal code extraction for coding problems
      const codeMatch = responseContent.match(/```(?:\w+)?\s*([\s\S]*?)```/);
      const code = codeMatch ? codeMatch[1].trim() : responseContent;

      // Extract thoughts, looking for bullet points or numbered lists
      const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)(?:Time complexity:|$)/i;
      const thoughtsMatch = responseContent.match(thoughtsRegex);
      let thoughts: string[] = [];

      if (thoughtsMatch && thoughtsMatch[1]) {
        // Extract bullet points or numbered items
        const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
        if (bulletPoints) {
          thoughts = bulletPoints.map((point: string) =>
            point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
          ).filter(Boolean);
        } else {
          // If no bullet points found, split by newlines and filter empty lines
          thoughts = thoughtsMatch[1].split('\n')
            .map((line: string) => line.trim())
            .filter(Boolean);
        }
      }

      // Extract complexity information
      const timeComplexityPattern = /Time complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Space complexity|$))/i;
      const spaceComplexityPattern = /Space complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:[A-Z]|$))/i;

      let timeComplexity = "O(n) - Linear time complexity because we only iterate through the array once. Each element is processed exactly one time, and the hashmap lookups are O(1) operations.";
      let spaceComplexity = "O(n) - Linear space complexity because we store elements in the hashmap. In the worst case, we might need to store all elements before finding the solution pair.";

      const timeMatch = responseContent.match(timeComplexityPattern);
      if (timeMatch && timeMatch[1]) {
        timeComplexity = timeMatch[1].trim();
        if (!timeComplexity.match(/O\([^)]+\)/i)) {
          timeComplexity = `O(n) - ${timeComplexity}`;
        } else if (!timeComplexity.includes('-') && !timeComplexity.includes('because')) {
          const notationMatch = timeComplexity.match(/O\([^)]+\)/i);
          if (notationMatch) {
            const notation = notationMatch[0];
            const rest = timeComplexity.replace(notation, '').trim();
            timeComplexity = `${notation} - ${rest}`;
          }
        }
      }

      const spaceMatch = responseContent.match(spaceComplexityPattern);
      if (spaceMatch && spaceMatch[1]) {
        spaceComplexity = spaceMatch[1].trim();
        if (!spaceComplexity.match(/O\([^)]+\)/i)) {
          spaceComplexity = `O(n) - ${spaceComplexity}`;
        } else if (!spaceComplexity.includes('-') && !spaceComplexity.includes('because')) {
          const notationMatch = spaceComplexity.match(/O\([^)]+\)/i);
          if (notationMatch) {
            const notation = notationMatch[0];
            const rest = spaceComplexity.replace(notation, '').trim();
            spaceComplexity = `${notation} - ${rest}`;
          }
        }
      }

      const formattedResponse = {
        code: code,
        thoughts: thoughts.length > 0 ? thoughts : ["Solution approach based on efficiency and readability"],
        time_complexity: timeComplexity,
        space_complexity: spaceComplexity
      };

      return { success: true, data: formattedResponse };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      }

      console.error("Solution generation error:", error);
      return { success: false, error: error.message || "Failed to generate solution" };
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        });
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      let debugContent;

      if (config.apiProvider === "openai") {
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        const messages = [
          {
            role: "system" as const,
            content: `You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

Your response MUST follow this exact structure with these section headers (use ### for headers):
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution. Here are screenshots of my code, the errors or test cases. Please provide a detailed analysis with:
1. What issues you found in my code
2. Specific improvements and corrections
3. Any optimizations that would make the solution better
4. A clear explanation of the changes needed`
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        if (mainWindow) {
          mainWindow.webContents.send("processing-status", {
            message: "Analyzing code and generating debug feedback...",
            progress: 60
          });
        }

        const debugResponse = await this.openaiClient.chat.completions.create({
          model: config.debuggingModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });

        debugContent = debugResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini...",
              progress: 60
            });
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.debuggingModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          debugContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for debugging:", error);
          return {
            success: false,
            error: "Failed to process debug request with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: debugPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Claude...",
              progress: 60
            });
          }

          const response = await this.anthropicClient.messages.create({
            model: config.debuggingModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          debugContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for debugging:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process debug request with Anthropic API. Please check your API key or try again later."
          };
        }
      }


      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Debug analysis complete",
          progress: 100
        });
      }

      let extractedCode = "// Debug mode - see analysis below";
      const codeMatch = debugContent.match(/```(?:[a-zA-Z]+)?([\s\S]*?)```/);
      if (codeMatch && codeMatch[1]) {
        extractedCode = codeMatch[1].trim();
      }

      let formattedDebugContent = debugContent;

      if (!debugContent.includes('# ') && !debugContent.includes('## ')) {
        formattedDebugContent = debugContent
          .replace(/issues identified|problems found|bugs found/i, '## Issues Identified')
          .replace(/code improvements|improvements|suggested changes/i, '## Code Improvements')
          .replace(/optimizations|performance improvements/i, '## Optimizations')
          .replace(/explanation|detailed analysis/i, '## Explanation');
      }

      const bulletPoints = formattedDebugContent.match(/(?:^|\n)[ ]*(?:[-*•]|\d+\.)[ ]+([^\n]+)/g);
      const thoughts = bulletPoints
        ? bulletPoints.map(point => point.replace(/^[ ]*(?:[-*•]|\d+\.)[ ]+/, '').trim()).slice(0, 5)
        : ["Debug analysis based on your screenshots"];

      const response = {
        code: extractedCode,
        debug_analysis: formattedDebugContent,
        thoughts: thoughts,
        time_complexity: "N/A - Debug mode",
        space_complexity: "N/A - Debug mode"
      };

      return { success: true, data: response };
    } catch (error: any) {
      console.error("Debug processing error:", error);
      return { success: false, error: error.message || "Failed to process debug request" };
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    this.deps.setHasDebugged(false)

    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }
}
